import cv2
import numpy as np
import os
from pathlib import Path
from scipy.ndimage import binary_fill_holes, binary_dilation
from skimage.segmentation import find_boundaries

class RoomSegmenter:
    def __init__(self,
                 image_dir,
                 label_dir,
                 output_dir="output",
                 area_thresh=100):

        self.image_dir = image_dir
        self.label_dir = label_dir
        self.output_dir = output_dir
        self.area_thresh = area_thresh

        self.class_names = {
            0: "bed_grounded",
            1: "bed_highleg",
            2: "sofa_grounded",
            3: "sofa_highleg",
            4: "door",
            5: "dining_table_set"
        }

        self.room_type_map = {
            0: "bedroom",
            1: "bedroom",
            2: "livingroom",
            3: "livingroom",
            5: "diningroom",
            6: "kitchen"
        }

        self.room_color_map = {
            "bedroom": (128, 128, 255),
            "livingroom": (128, 255, 128),
            "diningroom": (255, 128, 128),
            "kitchen": (0, 255, 255),
            "unknow": (255, 192, 0)
        }

        self.object_color_map = {
            "bed_grounded": (160, 160, 255),
            "bed_highleg": (100, 100, 255),
            "sofa_grounded": (160, 255, 160),
            "sofa_highleg": (100, 255, 100),
            "door": (255, 0, 255),
            "dining_table_set": (0, 128, 255)
        }

        self.font = cv2.FONT_HERSHEY_SIMPLEX

    def load_yolo_labels(self, label_file, img_w, img_h):
        boxes, classes = [], []
        with open(label_file, 'r') as f:
            for line in f:
                cls, cx, cy, w, h = map(float, line.strip().split())
                cls = int(cls)
                x1 = int((cx - w / 2) * img_w)
                y1 = int((cy - h / 2) * img_h)
                x2 = int((cx + w / 2) * img_w)
                y2 = int((cy + h / 2) * img_h)
                boxes.append([x1, y1, x2, y2])
                classes.append(cls)
        return boxes, classes
    
    
    def ray_to_boundary_intersection(self, start_x, start_y, dx, dy, boundary_mask):
        """
        从 (start_x, start_y) 向 dx, dy 方向发射，直到遇到边界像素（boundary_mask为True）
        """
        h, w = boundary_mask.shape
        x, y = start_x, start_y
        while 0 <= x < w and 0 <= y < h:
            if boundary_mask[int(y), int(x)]:
                return int(x), int(y)
            x += dx
            y += dy
        return None

    def get_diningroom_by_boundary_extension(self, labels_im, boxes, classes):
        """
        根据 dining_table_set 的三边发射线 + 底边，确定最小 diningroom 区域
        """
        boundary_mask = find_boundaries(labels_im, mode='outer')

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls != 5:  # dining_table_set
                continue

            # 三条边：top、left、right 上发射线
            edge_points = {
                "top_left": (x1, y1),
                "top_right": (x2, y1),
                "mid_left": (x1, (y1 + y2) // 2),
                "mid_right": (x2, (y1 + y2) // 2)
            }

            directions = {
                "top": (0, -1),
                "left": (-1, 0),
                "right": (1, 0)
            }

            hits = []

            for name, (sx, sy) in edge_points.items():
                for dname, (dx, dy) in directions.items():
                    if dname in name:  # 控制只发指定方向
                        hit = self.ray_to_boundary_intersection(sx, sy, dx, dy, boundary_mask)
                        if hit:
                            hits.append(hit)

            # 加入下边作为边界（非射线）
            hits.append((x1, y2))
            hits.append((x2, y2))

            xs = [pt[0] for pt in hits]
            ys = [pt[1] for pt in hits]
            min_x = max(0, min(xs))
            max_x = min(labels_im.shape[1] - 1, max(xs))
            min_y = max(0, min(ys))
            max_y = min(labels_im.shape[0] - 1, max(ys))

            return min_x, min_y, max_x, max_y

        return None

    def _determine_room_type(self, present_classes, mask, boxes, classes):
        """
        改进的房间类型判断逻辑，特别针对餐厅分割
        """
        h, w = mask.shape

        # 如果区域包含dining_table_set，进一步分析
        if 5 in present_classes:
            # 计算dining_table_set在该区域中的覆盖度和位置
            dining_coverage = self._calculate_dining_coverage(mask, boxes, classes)

            # 如果dining_table_set覆盖度高且位置合理，判断为餐厅
            if dining_coverage > 0.3:  # 可调参数
                # 检查是否同时有沙发，如果有且沙发覆盖度更高，可能是客厅
                if 2 in present_classes or 3 in present_classes:
                    sofa_coverage = self._calculate_sofa_coverage(mask, boxes, classes)
                    if sofa_coverage > dining_coverage * 1.5:  # 沙发覆盖度明显更高
                        return "livingroom"
                return "diningroom"
            else:
                # dining_table_set覆盖度低，可能是客厅的一部分
                if 2 in present_classes or 3 in present_classes:
                    return "livingroom"

        # 传统的房间类型判断逻辑
        if 2 in present_classes or 3 in present_classes:
            return "livingroom"

        # 单一家具类型判断
        for cls in present_classes:
            if cls in self.room_type_map:
                return self.room_type_map[cls]

        return "unknow"

    def _calculate_dining_coverage(self, mask, boxes, classes):
        """
        计算dining_table_set在当前区域中的覆盖度
        """
        total_dining_area = 0
        mask_area = np.sum(mask)

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 5:  # dining_table_set
                # 计算dining_table_set与mask的重叠区域
                dining_mask = np.zeros_like(mask, dtype=bool)
                dining_mask[y1:y2, x1:x2] = True
                overlap = np.sum(mask & dining_mask)
                total_dining_area += overlap

        return total_dining_area / mask_area if mask_area > 0 else 0

    def _calculate_sofa_coverage(self, mask, boxes, classes):
        """
        计算沙发在当前区域中的覆盖度
        """
        total_sofa_area = 0
        mask_area = np.sum(mask)

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls in [2, 3]:  # sofa_grounded, sofa_highleg
                # 计算沙发与mask的重叠区域
                sofa_mask = np.zeros_like(mask, dtype=bool)
                sofa_mask[y1:y2, x1:x2] = True
                overlap = np.sum(mask & sofa_mask)
                total_sofa_area += overlap

        return total_sofa_area / mask_area if mask_area > 0 else 0

    def create_diningroom_mask(self, labels_im, boxes, classes):
        """
        基于dining_table_set创建更精确的餐厅区域mask
        """
        h, w = labels_im.shape
        dining_mask = np.zeros((h, w), dtype=bool)

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 5:  # dining_table_set
                # 以dining_table_set为中心，创建扩展区域
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2

                # 计算扩展半径（基于dining_table_set的大小）
                table_w = x2 - x1
                table_h = y2 - y1
                expand_radius = max(table_w, table_h) * 1.5  # 可调参数

                # 创建圆形或矩形扩展区域
                mask_x1 = max(0, int(center_x - expand_radius))
                mask_y1 = max(0, int(center_y - expand_radius))
                mask_x2 = min(w, int(center_x + expand_radius))
                mask_y2 = min(h, int(center_y + expand_radius))

                # 使用边界检测来限制扩展
                boundary_mask = find_boundaries(labels_im, mode='outer')

                # 从餐桌中心向外扩展，直到遇到边界
                for y in range(mask_y1, mask_y2):
                    for x in range(mask_x1, mask_x2):
                        # 检查是否在合理距离内且未遇到边界
                        dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                        if dist <= expand_radius and not boundary_mask[y, x]:
                            dining_mask[y, x] = True

        return dining_mask



    def bridge_merge_labels(self, labels_im, room_type_for_label, kernel_size=5, iterations=3, overlap_thresh=20):
        """
        膨胀同类房间的连通域掩码，若膨胀后有重叠则合并，减少被门分割导致的断裂。

        labels_im: int label image
        room_type_for_label: dict {label: room_type string}
        kernel_size: 膨胀核大小
        iterations: 膨胀次数
        overlap_thresh: 重叠像素阈值，超过则合并

        返回合并后的labels_im和更新的room_type_for_label
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        merged_labels = labels_im.copy()

        # 获取所有标签（排除0背景）
        labels = [l for l in np.unique(labels_im) if l != 0]

        # 记录掩码和膨胀掩码
        masks = {l: (merged_labels == l) for l in labels}
        dilated_masks = {l: cv2.dilate(masks[l].astype(np.uint8), kernel, iterations=iterations).astype(bool) for l in labels}

        # 合并映射，初始自己指向自己
        parent = {l: l for l in labels}

        def find(x):
            while parent[x] != x:
                parent[x] = parent[parent[x]]
                x = parent[x]
            return x

        def union(a, b):
            pa, pb = find(a), find(b)
            if pa != pb:
                parent[pb] = pa

        # 两两比较重叠且同类房间的连通域合并
        for i, l1 in enumerate(labels):
            for l2 in labels[i+1:]:
                if room_type_for_label.get(l1) != room_type_for_label.get(l2):
                    continue
                overlap = np.sum(dilated_masks[l1] & masks[l2])
                if overlap > overlap_thresh:
                    union(l1, l2)

        # 重新标记连通域
        label_map = {}
        new_label = 1
        new_room_type_for_label = {}
        new_labels_im = np.zeros_like(labels_im)

        for l in labels:
            root = find(l)
            if root not in label_map:
                label_map[root] = new_label
                new_room_type_for_label[new_label] = room_type_for_label.get(root, "unknow")
                new_label += 1
            label_map[l] = label_map[root]

        for l in labels:
            new_labels_im[merged_labels == l] = label_map[l]

        return new_labels_im, new_room_type_for_label


    def merge_regions(self, labels_im, num_labels):
        # 构建每个连通区域的掩码和面积信息
        region_masks = {}
        region_areas = {}

        for i in range(1, num_labels):
            mask = (labels_im == i)
            area = np.sum(mask)
            region_masks[i] = mask
            region_areas[i] = area

        # 从大区域向小区域尝试合并
        merged_labels = labels_im.copy()
        label_list = sorted(region_areas.items(), key=lambda x: x[1], reverse=True)  # 按面积从大到小排序

        for big_label, big_area in label_list:
            big_mask = region_masks[big_label]
            big_mask_dilated = binary_dilation(big_mask, iterations=5)  # 向外膨胀一圈，避免紧贴边界

            for small_label, small_area in label_list:
                if small_label == big_label or small_area == 0:
                    continue
                small_mask = region_masks[small_label]

                # 如果小区域大部分像素都在大区域膨胀范围内，则归并
                overlap = big_mask_dilated & small_mask
                if np.sum(overlap) > 0.2 * np.sum(small_mask):  # 可调阈值：80%
                    merged_labels[merged_labels == small_label] = big_label  # 合并
                    region_masks[big_label] = (merged_labels == big_label)  # 更新大区域mask
                    region_areas[big_label] += region_areas[small_label]
                    region_areas[small_label] = 0  # 被合并掉


        return merged_labels, region_areas

    def classify_and_visualize(self, image, labels_im, boxes, classes, region_areas, save_dir, img_name):
        h, w = labels_im.shape
        vis_image = image.copy()
        color_mask = np.zeros_like(image)
        room_idx = 0
        room_type_for_label = {}
        room_info = []  # 存储所有房间信息

        # 找最大连通域label
        max_region_label = max(region_areas, key=region_areas.get)
        max_region_area = region_areas[max_region_label]

        for i in range(1, np.max(labels_im)+1):
            if region_areas.get(i, 0) < self.area_thresh:
                continue
            mask = (labels_im == i)
            present_classes = set()
            for (x1, y1, x2, y2), cls in zip(boxes, classes):
                if cls == 4:
                    continue
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                win = 1
                in_region = False
                for dx in range(-win, win + 1):
                    for dy in range(-win, win + 1):
                        nx, ny = cx + dx, cy + dy
                        if 0 <= nx < w and 0 <= ny < h and mask[ny, nx]:
                            in_region = True
                            break
                    if in_region:
                        present_classes.add(cls)

            # 改进的房间类型判断逻辑
            room_type = self._determine_room_type(present_classes, mask, boxes, classes)

            if i == max_region_label and room_type == "diningroom" and max_region_area > 5000:
                room_type = "livingroom"

            
            mask_filled = binary_fill_holes(mask)
            mask_uint8 = (mask_filled * 255).astype(np.uint8)
            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            filled_mask = np.zeros_like(mask_uint8)
            cv2.drawContours(filled_mask, contours, -1, 255, thickness=-1)
            filled_mask_bool = filled_mask.astype(bool)

            color = self.room_color_map[room_type]
            vis_image[filled_mask_bool] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[filled_mask_bool]
            color_mask[filled_mask_bool] = color

            ys, xs = np.where(filled_mask_bool)
            if len(xs) > 0 and len(ys) > 0:
                cx, cy = int(np.mean(xs)), int(np.mean(ys))
                cv2.putText(vis_image, room_type, (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)

            # # 保存mask
            cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask_filled.astype(np.uint8) * 255))
            room_idx += 1
        #     # 保存房间信息
        #     room_info.append({
        #         "label": i,
        #         "type": room_type,
        #         "center": (cx, cy),
        #         "mask": filled_mask_bool,
        #         "area": np.sum(filled_mask_bool)
        #     })
        # # == 1. 记录 dining_table_set 中心点 ==
        # dining_centers = []
        # for (x1, y1, x2, y2), cls in zip(boxes, classes):
        #     if cls == 5:  # dining_table_set
        #         cx = (x1 + x2) // 2
        #         cy = (y1 + y2) // 2
        #         dining_centers.append((cx, cy))

        # # == 2. 找出所有未知类型房间 ==
        # unknow_rooms = [room for room in room_info if room["type"] == "unknow"]

        # # == 3. 计算与 dining_table_set 的最短距离 ==
        # min_dist = float("inf")
        # kitchen_candidate = None

        # for room in unknow_rooms:
        #     rcx, rcy = room["center"]
        #     for dcx, dcy in dining_centers:
        #         dist = np.sqrt((rcx - dcx)**2 + (rcy - dcy)**2)
        #         if dist < min_dist:
        #             min_dist = dist
        #             kitchen_candidate = room

        # # == 4. 设置为 kitchen 并绘制 ==
        # if kitchen_candidate:
        #     kitchen_candidate["type"] = "kitchen"
        #     color = self.room_color_map.get("kitchen")
        #     mask = kitchen_candidate["mask"]
        #     vis_image[mask] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[mask]
        #     color_mask[mask] = color
        #     cx, cy = kitchen_candidate["center"]
        #     cv2.putText(vis_image, "kitchen", (cx - 40, cy), self.font, 0.5, (0, 0, 0), 2)
        #     cv2.imwrite(f"{save_dir}/kitchen_{room_idx}.png", (mask.astype(np.uint8) * 255))
        #     room_idx += 1
         # === 利用 dining_table_set 生成 diningroom 区域 ===
        dining_box = self.get_diningroom_by_boundary_extension(labels_im, boxes, classes)
        if dining_box:
            dx1, dy1, dx2, dy2 = dining_box
            cv2.rectangle(vis_image, (dx1, dy1), (dx2, dy2), (0, 165, 255), 2)
            cv2.putText(vis_image, "diningroom", (dx1 + 5, dy1 - 5), self.font, 0.5, (0, 100, 255), 2)

            # 保存 mask 图像
            dining_mask = np.zeros_like(labels_im, dtype=np.uint8)
            dining_mask[dy1:dy2, dx1:dx2] = 255
            cv2.imwrite(f"{save_dir}/diningroom_mask.png", dining_mask)
        # 绘制门框
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

        # 绘制目标框和中心点
        boxed_image = image.copy()
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            label = self.class_names.get(cls, str(cls))
            color = self.object_color_map.get(label, (0, 255, 255))
            cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)
            if label != "door":
                cx = (x1 + x2) // 2
                cy = (y1 + y2) // 2
                cv2.circle(boxed_image, (cx, cy), 3, (0, 0, 255), -1)
        # 新增：基于类别合并连通域，减少断裂
        labels_im, room_type_for_label = self.bridge_merge_labels(labels_im, room_type_for_label)

        # 重新计算区域面积等（可复用merge_regions逻辑）
        _, region_areas = self.merge_regions(labels_im, np.max(labels_im) + 1)
        
        # 拼接结果图
        vis_h, vis_w = vis_image.shape[:2]
        legend_height = 100
        final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
        final_image[legend_height:, :vis_w] = vis_image
        final_image[legend_height:, vis_w:] = boxed_image
        cv2.line(final_image, (vis_w, legend_height), (vis_w, vis_h + legend_height), (255, 255, 255), 2)

        # 添加图例
        self._draw_legend(final_image, legend_height)

        # 保存结果
        cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
        cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
        cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
        cv2.imwrite(f"{save_dir}/{img_name}_final_result.png", final_image)

    def _draw_legend(self, final_image, legend_height):
        legend_font = 0.45
        y_offset_2 = 20
        y_offset_3 = 50
        y_offset_4 = 80
        x_step = 150

        for i, label in enumerate(["bed_grounded", "bed_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["sofa_grounded", "sofa_highleg"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), self.font, legend_font, (0, 0, 0), 1)

        for i, label in enumerate(["door", "dining_table_set"]):
            color = self.object_color_map[label]
            x = 10 + i * x_step
            cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
            cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), self.font, legend_font, (0, 0, 0), 1)

    def process_image(self, image_path, label_path):
        img_name = Path(image_path).stem
        image = cv2.imread(image_path)
        if image is None:
            print(f"⚠️ Failed to load image: {image_path}")
            return
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape

        boxes, classes = self.load_yolo_labels(label_path, w, h)

        _, binary = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)

        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4:  # door
                cv2.rectangle(binary, (x1, y1), (x2, y2 + 1), 0, -1)
            elif cls in [0, 1, 2, 3, 5]:
                cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)

        num_labels, labels_im = cv2.connectedComponents(binary)
        merged_labels, region_areas = self.merge_regions(labels_im, num_labels)

        save_dir = os.path.join(self.output_dir, img_name)
        os.makedirs(save_dir, exist_ok=True)
        self.classify_and_visualize(image, merged_labels, boxes, classes, region_areas, save_dir, img_name)
        print(f"✅ Processed {img_name}, Saved to {save_dir}")

    def batch_process(self):
        os.makedirs(self.output_dir, exist_ok=True)
        for img_file in sorted(os.listdir(self.image_dir)):
            if not img_file.lower().endswith((".jpg", ".png")):
                continue
            img_path = os.path.join(self.image_dir, img_file)
            label_path = os.path.join(self.label_dir, Path(img_file).with_suffix(".txt"))
            if not os.path.exists(label_path):
                print(f"⚠️ Label not found for {img_file}")
                continue
            self.process_image(img_path, label_path)


if __name__ == "__main__":
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"
    OUTPUT_DIR = "output"
    AREA_THRESH = 100

    segmenter = RoomSegmenter(IMAGE_DIR, LABEL_DIR, OUTPUT_DIR, AREA_THRESH)
    segmenter.batch_process()
